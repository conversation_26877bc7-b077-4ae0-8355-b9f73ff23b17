import { SlashCommandBuilder, EmbedBuilder } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import Store from '../../models/Store.js';
import Product from '../../models/Product.js';
import StockItem from '../../models/StockItem.js';
import ShoppingCart from '../../models/ShoppingCart.js';

export default {
    data: new SlashCommandBuilder()
        .setName('diagnostico-carrinho')
        .setDescription('Diagnóstica problemas no sistema de carrinho de compras (apenas administradores)'),
    
    async execute(interaction) {
        try {
            // Verifica se o usuário é administrador
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem usar este comando.',
                    ephemeral: true
                });
            }

            await interaction.deferReply({ ephemeral: true });

            const guildId = interaction.guild.id;
            const diagnosticResults = [];

            // 1. Verificar lojas no servidor
            const stores = await Store.find({ guildId, isActive: true });
            diagnosticResults.push(`🏪 **Lojas encontradas:** ${stores.length}`);

            if (stores.length === 0) {
                diagnosticResults.push('❌ **PROBLEMA:** Não há lojas criadas neste servidor.');
                diagnosticResults.push('💡 **Solução:** Use `/criar-loja` para criar uma loja.');
            } else {
                for (const store of stores) {
                    const channel = interaction.guild.channels.cache.get(store.channelId);
                    const channelStatus = channel ? '✅ Canal existe' : '❌ Canal não encontrado';
                    diagnosticResults.push(`   • ${store.name}: ${channelStatus}`);
                    
                    // Verificar se a mensagem da loja existe
                    if (channel && store.messageId) {
                        try {
                            await channel.messages.fetch(store.messageId);
                            diagnosticResults.push(`     📨 Mensagem da loja: ✅ Existe`);
                        } catch (error) {
                            diagnosticResults.push(`     📨 Mensagem da loja: ❌ Não encontrada`);
                        }
                    }
                }
            }

            // 2. Verificar produtos
            const storeIds = stores.map(store => store._id);
            const products = await Product.find({ 
                storeId: { $in: storeIds },
                status: 'active'
            });
            
            diagnosticResults.push(`\n📦 **Produtos ativos:** ${products.length}`);

            if (products.length === 0 && stores.length > 0) {
                diagnosticResults.push('❌ **PROBLEMA:** Não há produtos ativos nas lojas.');
                diagnosticResults.push('💡 **Solução:** Use `/criar-produto` para adicionar produtos.');
            }

            // 3. Verificar estoque
            let totalStock = 0;
            let productsWithStock = 0;

            for (const product of products) {
                const stockCount = await StockItem.countByProduct(product._id, 'available');
                totalStock += stockCount;
                if (stockCount > 0) {
                    productsWithStock++;
                }
            }

            diagnosticResults.push(`📊 **Produtos com estoque:** ${productsWithStock}/${products.length}`);
            diagnosticResults.push(`📈 **Total de itens em estoque:** ${totalStock}`);

            if (productsWithStock === 0 && products.length > 0) {
                diagnosticResults.push('❌ **PROBLEMA:** Nenhum produto tem estoque disponível.');
                diagnosticResults.push('💡 **Solução:** Use `/criar-estoque` para adicionar estoque aos produtos.');
            }

            // 4. Verificar carrinhos ativos
            const activeCarts = await ShoppingCart.find({ 
                guildId,
                status: 'active'
            });
            
            diagnosticResults.push(`\n🛒 **Carrinhos ativos:** ${activeCarts.length}`);

            // 5. Verificar permissões do bot
            const botMember = interaction.guild.members.me;
            const requiredPermissions = ['ManageChannels', 'ManageRoles', 'SendMessages', 'EmbedLinks', 'ViewChannel'];
            const missingPermissions = requiredPermissions.filter(perm => 
                !botMember.permissions.has(perm)
            );

            if (missingPermissions.length > 0) {
                diagnosticResults.push(`\n❌ **PROBLEMA:** Bot não tem permissões necessárias: ${missingPermissions.join(', ')}`);
                diagnosticResults.push('💡 **Solução:** Conceda as permissões necessárias ao bot.');
            } else {
                diagnosticResults.push(`\n✅ **Permissões do bot:** OK`);
            }

            // 6. Resumo do diagnóstico
            let status = '✅ Sistema funcionando';
            if (stores.length === 0) {
                status = '❌ Sem lojas criadas';
            } else if (products.length === 0) {
                status = '❌ Sem produtos cadastrados';
            } else if (productsWithStock === 0) {
                status = '❌ Sem estoque disponível';
            } else if (missingPermissions.length > 0) {
                status = '⚠️ Problemas de permissão';
            }

            // Criar embed de resposta
            const embed = new EmbedBuilder()
                .setTitle('🔍 Diagnóstico do Sistema de Carrinho')
                .setDescription(diagnosticResults.join('\n'))
                .setColor(status.startsWith('✅') ? 'Green' : status.startsWith('❌') ? 'Red' : 'Yellow')
                .addFields({
                    name: '📋 Status Geral',
                    value: status,
                    inline: false
                })
                .setTimestamp()
                .setFooter({ 
                    text: 'Use os comandos sugeridos para resolver os problemas encontrados' 
                });

            await interaction.editReply({
                embeds: [embed]
            });

            // Log do diagnóstico
            await logger.userAction('Diagnóstico do carrinho executado', {
                guildId: interaction.guild.id,
                userId: interaction.user.id,
                command: 'diagnostico-carrinho'
            }, {
                stores: stores.length,
                products: products.length,
                productsWithStock,
                totalStock,
                activeCarts: activeCarts.length,
                status
            });

        } catch (error) {
            logger.error('Erro ao executar diagnóstico do carrinho:', error);
            
            const errorMessage = {
                content: '❌ Erro ao executar diagnóstico do sistema.'
            };

            if (interaction.deferred) {
                await interaction.editReply(errorMessage);
            } else {
                await interaction.reply({ ...errorMessage, ephemeral: true });
            }
        }
    }
};
