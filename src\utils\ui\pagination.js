import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ActionRow<PERSON>uilder, <PERSON><PERSON><PERSON><PERSON>er, ButtonStyle } from 'discord.js';
import { logger } from '../logging/logger.js';

/**
 * Advanced Pagination System for Discord Bot
 * 
 * Provides efficient pagination for:
 * - Stock listings in store management
 * - Log history viewing
 * - User transaction histories
 * - Product catalogs
 * - Order histories
 */

class PaginationManager {
    constructor() {
        this.activePaginations = new Map();
        this.defaultPageSize = 10;
        this.maxPageSize = 25;
        this.paginationTimeout = 300000; // 5 minutes
        
        // Clean up expired paginations every minute
        setInterval(() => this.cleanupExpiredPaginations(), 60000);
    }
    
    /**
     * Create paginated embed for data
     * @param {Object} options - Pagination options
     * @param {Array} options.data - Array of data to paginate
     * @param {number} options.page - Current page (0-indexed)
     * @param {number} options.pageSize - Items per page
     * @param {string} options.title - Embed title
     * @param {string} options.description - Embed description
     * @param {Function} options.formatItem - Function to format each item
     * @param {Object} options.embedOptions - Additional embed options
     * @returns {Object} Embed and components
     */
    createPaginatedEmbed(options) {
        const {
            data,
            page = 0,
            pageSize = this.defaultPageSize,
            title,
            description = '',
            formatItem,
            embedOptions = {}
        } = options;
        
        const totalPages = Math.ceil(data.length / pageSize);
        const currentPage = Math.max(0, Math.min(page, totalPages - 1));
        const startIndex = currentPage * pageSize;
        const endIndex = Math.min(startIndex + pageSize, data.length);
        const pageData = data.slice(startIndex, endIndex);
        
        // Create embed
        const embed = new EmbedBuilder()
            .setTitle(title)
            .setDescription(description)
            .setColor(embedOptions.color || 0x0099FF)
            .setTimestamp();
        
        // Add fields for each item
        if (pageData.length > 0) {
            pageData.forEach((item, index) => {
                const formattedItem = formatItem(item, startIndex + index);
                if (formattedItem.name && formattedItem.value) {
                    embed.addFields({
                        name: formattedItem.name,
                        value: formattedItem.value,
                        inline: formattedItem.inline || false
                    });
                }
            });
        } else {
            embed.addFields({
                name: 'Nenhum item encontrado',
                value: 'Não há dados para exibir.',
                inline: false
            });
        }
        
        // Add footer with page info
        embed.setFooter({
            text: `Página ${currentPage + 1} de ${totalPages} • Total: ${data.length} itens`
        });
        
        // Create navigation buttons
        const components = this.createNavigationButtons(currentPage, totalPages);
        
        return {
            embed,
            components: components.length > 0 ? [components] : [],
            currentPage,
            totalPages,
            hasData: pageData.length > 0
        };
    }
    
    /**
     * Create navigation buttons for pagination
     * @param {number} currentPage - Current page index
     * @param {number} totalPages - Total number of pages
     * @returns {ActionRowBuilder} Button row
     */
    createNavigationButtons(currentPage, totalPages) {
        if (totalPages <= 1) return new ActionRowBuilder();
        
        const row = new ActionRowBuilder();
        
        // First page button
        row.addComponents(
            new ButtonBuilder()
                .setCustomId('pagination_first')
                .setLabel('⏮️')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage === 0)
        );
        
        // Previous page button
        row.addComponents(
            new ButtonBuilder()
                .setCustomId('pagination_prev')
                .setLabel('◀️')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage === 0)
        );
        
        // Page indicator
        row.addComponents(
            new ButtonBuilder()
                .setCustomId('pagination_info')
                .setLabel(`${currentPage + 1}/${totalPages}`)
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(true)
        );
        
        // Next page button
        row.addComponents(
            new ButtonBuilder()
                .setCustomId('pagination_next')
                .setLabel('▶️')
                .setStyle(ButtonStyle.Primary)
                .setDisabled(currentPage >= totalPages - 1)
        );
        
        // Last page button
        row.addComponents(
            new ButtonBuilder()
                .setCustomId('pagination_last')
                .setLabel('⏭️')
                .setStyle(ButtonStyle.Secondary)
                .setDisabled(currentPage >= totalPages - 1)
        );
        
        return row;
    }
    
    /**
     * Register pagination session
     * @param {string} messageId - Discord message ID
     * @param {Object} paginationData - Pagination configuration
     */
    registerPagination(messageId, paginationData) {
        this.activePaginations.set(messageId, {
            ...paginationData,
            createdAt: Date.now(),
            lastInteraction: Date.now()
        });
        
        logger.debug(`Registered pagination for message: ${messageId}`);
    }
    
    /**
     * Handle pagination button interaction
     * @param {Object} interaction - Discord interaction
     * @returns {Object|null} Updated embed and components
     */
    async handlePaginationInteraction(interaction) {
        const messageId = interaction.message.id;
        const paginationData = this.activePaginations.get(messageId);
        
        if (!paginationData) {
            try {
                if (interaction.replied) {
                    await interaction.followUp({
                        content: '❌ Esta paginação expirou. Execute o comando novamente.',
                        ephemeral: true
                    });
                } else if (interaction.deferred) {
                    await interaction.editReply({
                        content: '❌ Esta paginação expirou. Execute o comando novamente.',
                        ephemeral: true
                    });
                } else {
                    await interaction.reply({
                        content: '❌ Esta paginação expirou. Execute o comando novamente.',
                        ephemeral: true
                    });
                }
            } catch (replyError) {
                logger.error('Erro ao responder paginação expirada:', replyError);
            }
            return null;
        }
        
        // Update last interaction time
        paginationData.lastInteraction = Date.now();
        
        const { data, pageSize, title, description, formatItem, embedOptions } = paginationData;
        let { currentPage } = paginationData;
        const totalPages = Math.ceil(data.length / pageSize);
        
        // Handle button actions
        switch (interaction.customId) {
            case 'pagination_first':
                currentPage = 0;
                break;
            case 'pagination_prev':
                currentPage = Math.max(0, currentPage - 1);
                break;
            case 'pagination_next':
                currentPage = Math.min(totalPages - 1, currentPage + 1);
                break;
            case 'pagination_last':
                currentPage = totalPages - 1;
                break;
            default:
                return null;
        }
        
        // Update pagination data
        paginationData.currentPage = currentPage;
        this.activePaginations.set(messageId, paginationData);
        
        // Create updated embed
        const result = this.createPaginatedEmbed({
            data,
            page: currentPage,
            pageSize,
            title,
            description,
            formatItem,
            embedOptions
        });
        
        return result;
    }
    
    /**
     * Clean up expired pagination sessions
     */
    cleanupExpiredPaginations() {
        const now = Date.now();
        let cleanedCount = 0;
        
        for (const [messageId, paginationData] of this.activePaginations.entries()) {
            if (now - paginationData.lastInteraction > this.paginationTimeout) {
                this.activePaginations.delete(messageId);
                cleanedCount++;
            }
        }
        
        if (cleanedCount > 0) {
            logger.debug(`Cleaned up ${cleanedCount} expired pagination sessions`);
        }
    }
    
    /**
     * Remove specific pagination session
     * @param {string} messageId - Discord message ID
     */
    removePagination(messageId) {
        const removed = this.activePaginations.delete(messageId);
        if (removed) {
            logger.debug(`Removed pagination for message: ${messageId}`);
        }
        return removed;
    }
    
    /**
     * Get pagination statistics
     * @returns {Object} Statistics
     */
    getStatistics() {
        return {
            activePaginations: this.activePaginations.size,
            oldestPagination: this.activePaginations.size > 0 
                ? Math.min(...Array.from(this.activePaginations.values()).map(p => p.createdAt))
                : null
        };
    }
}

// Create singleton instance
const paginationManager = new PaginationManager();

/**
 * Predefined formatters for common data types
 */
export const formatters = {
    /**
     * Format stock items
     */
    stockItem: (item, index) => ({
        name: `📦 Item ${index + 1}`,
        value: `**Produto:** ${item.productName || 'N/A'}\n` +
               `**Status:** ${item.status}\n` +
               `**Conteúdo:** \`${item.content || 'N/A'}\`\n` +
               `**Criado:** <t:${Math.floor(new Date(item.createdAt).getTime() / 1000)}:R>`,
        inline: true
    }),
    
    /**
     * Format products
     */
    product: (item, index) => ({
        name: `🛍️ ${item.name}`,
        value: `**Preço:** R$ ${item.price.toFixed(2)}\n` +
               `**Estoque:** ${item.stock || 0}\n` +
               `**Status:** ${item.isActive ? '✅ Ativo' : '❌ Inativo'}\n` +
               `**ID:** \`${item._id}\``,
        inline: true
    }),
    
    /**
     * Format orders
     */
    order: (item, index) => ({
        name: `🧾 Pedido #${item.orderNumber || index + 1}`,
        value: `**Cliente:** <@${item.userId}>\n` +
               `**Status:** ${item.status}\n` +
               `**Total:** R$ ${item.total.toFixed(2)}\n` +
               `**Data:** <t:${Math.floor(new Date(item.createdAt).getTime() / 1000)}:R>`,
        inline: true
    }),
    
    /**
     * Format users
     */
    user: (item, index) => ({
        name: `👤 ${item.username || 'Usuário'}`,
        value: `**ID:** \`${item.userId}\`\n` +
               `**Saldo:** R$ ${item.balance.toFixed(2)}\n` +
               `**Total Gasto:** R$ ${item.totalSpent.toFixed(2)}\n` +
               `**Status:** ${item.isBlacklisted ? '🚫 Banido' : '✅ Ativo'}`,
        inline: true
    }),
    
    /**
     * Format logs
     */
    log: (item, index) => ({
        name: `📝 Log ${index + 1}`,
        value: `**Tipo:** ${item.type}\n` +
               `**Usuário:** <@${item.userId}>\n` +
               `**Ação:** ${item.action}\n` +
               `**Data:** <t:${Math.floor(new Date(item.timestamp).getTime() / 1000)}:R>`,
        inline: false
    })
};

/**
 * Helper functions for common pagination scenarios
 */
export const paginate = {
    /**
     * Create paginated stock listing
     */
    stock: (stockItems, page = 0, options = {}) => {
        return paginationManager.createPaginatedEmbed({
            data: stockItems,
            page,
            pageSize: options.pageSize || 10,
            title: options.title || '📦 Estoque',
            description: options.description || 'Lista de itens em estoque',
            formatItem: formatters.stockItem,
            embedOptions: { color: 0x00FF00 }
        });
    },
    
    /**
     * Create paginated product listing
     */
    products: (products, page = 0, options = {}) => {
        return paginationManager.createPaginatedEmbed({
            data: products,
            page,
            pageSize: options.pageSize || 8,
            title: options.title || '🛍️ Produtos',
            description: options.description || 'Lista de produtos disponíveis',
            formatItem: formatters.product,
            embedOptions: { color: 0x0099FF }
        });
    },
    
    /**
     * Create paginated order history
     */
    orders: (orders, page = 0, options = {}) => {
        return paginationManager.createPaginatedEmbed({
            data: orders,
            page,
            pageSize: options.pageSize || 5,
            title: options.title || '🧾 Histórico de Pedidos',
            description: options.description || 'Histórico de pedidos realizados',
            formatItem: formatters.order,
            embedOptions: { color: 0xFFAA00 }
        });
    },
    
    /**
     * Create paginated user listing
     */
    users: (users, page = 0, options = {}) => {
        return paginationManager.createPaginatedEmbed({
            data: users,
            page,
            pageSize: options.pageSize || 10,
            title: options.title || '👥 Usuários',
            description: options.description || 'Lista de usuários registrados',
            formatItem: formatters.user,
            embedOptions: { color: 0x9932CC }
        });
    },
    
    /**
     * Create paginated log history
     */
    logs: (logs, page = 0, options = {}) => {
        return paginationManager.createPaginatedEmbed({
            data: logs,
            page,
            pageSize: options.pageSize || 15,
            title: options.title || '📝 Logs do Sistema',
            description: options.description || 'Histórico de atividades do sistema',
            formatItem: formatters.log,
            embedOptions: { color: 0x808080 }
        });
    },
    
    /**
     * Register pagination for message
     */
    register: (messageId, paginationData) => {
        paginationManager.registerPagination(messageId, paginationData);
    },
    
    /**
     * Handle pagination interaction
     */
    handleInteraction: (interaction) => {
        return paginationManager.handlePaginationInteraction(interaction);
    },
    
    /**
     * Remove pagination
     */
    remove: (messageId) => {
        return paginationManager.removePagination(messageId);
    },
    
    /**
     * Get statistics
     */
    getStats: () => {
        return paginationManager.getStatistics();
    }
};

export default paginationManager;