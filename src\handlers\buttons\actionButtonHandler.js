import { PermissionFlagsBits, EmbedBuilder } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import Product from '../../models/Product.js';
import Store from '../../models/Store.js';
import StockItem from '../../models/StockItem.js';

/**
 * Handler para botões de ação (confirm, cancel, delete, view, search)
 */
export class ActionButtonHandler {
    /**
     * Manipula botões de confirmação
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} customId - ID customizado completo
     */
    static async handleConfirmButton(interaction, action, customId) {
        try {
            // Verificação de permissões
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem confirmar esta ação.',
                    ephemeral: true
                });
            }

            logger.info(`Botão de confirmação clicado: ${customId} por ${interaction.user.tag}`);

            if (action === 'delete' && customId.includes('stock')) {
                await this.handleConfirmDeleteStock(interaction, customId);
            } else if (action === 'delete' && customId.includes('product')) {
                await this.handleConfirmDeleteProduct(interaction, customId);
            } else {
                await interaction.reply({
                    content: '❌ Ação de confirmação não reconhecida.',
                    ephemeral: true
                });
            }
        } catch (error) {
            logger.error('Erro no handler de botão de confirmação:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula botões de cancelamento
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} customId - ID customizado completo
     */
    static async handleCancelButton(interaction, action, customId) {
        try {
            logger.info(`Botão de cancelamento clicado: ${customId} por ${interaction.user.tag}`);

            // Tratamento específico para diferentes tipos de cancelamento
            if (customId === 'cancel_delete_product') {
                await interaction.update({
                    content: '❌ **Operação cancelada**\n\nA remoção de produto foi cancelada.',
                    embeds: [],
                    components: [],
                    ephemeral: true
                });
            } else {
                await interaction.reply({
                    content: '❌ Ação cancelada.',
                    ephemeral: true
                });
            }
        } catch (error) {
            logger.error('Erro no handler de botão de cancelamento:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula botões de exclusão
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} id - ID relacionado ao botão
     */
    static async handleDeleteButton(interaction, action, id) {
        try {
            // Verificação de permissões
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem excluir itens.',
                    ephemeral: true
                });
            }

            logger.info(`Botão de exclusão clicado: ${action} (ID: ${id}) por ${interaction.user.tag}`);

            await interaction.reply({
                content: `🗑️ Funcionalidade de exclusão será implementada em breve! (ID: ${id})`,
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de botão de exclusão:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula botões de visualização
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} customId - ID customizado completo
     */
    static async handleViewButton(interaction, action, customId) {
        try {
            logger.info(`Botão de visualização clicado: ${customId} por ${interaction.user.tag}`);

            // Parse do customId para determinar o tipo de visualização
            if (customId.startsWith('view_product_')) {
                const productId = customId.replace('view_product_', '');
                await this.handleViewProduct(interaction, productId);
            } else if (customId.startsWith('view_store_')) {
                const storeId = customId.replace('view_store_', '');
                await this.handleViewStore(interaction, storeId);
            } else if (customId.startsWith('view_order_')) {
                const orderId = customId.replace('view_order_', '');
                await this.handleViewOrder(interaction, orderId);
            } else {
                await interaction.reply({
                    content: `👁️ Tipo de visualização não reconhecido: ${customId}`,
                    ephemeral: true
                });
            }
        } catch (error) {
            logger.error('Erro no handler de botão de visualização:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Visualiza detalhes de um produto
     * @param {Object} interaction - Interação do Discord
     * @param {string} productId - ID do produto
     */
    static async handleViewProduct(interaction, productId) {
        try {
            const product = await Product.findById(productId).populate('storeId');

            if (!product) {
                return await interaction.reply({
                    content: '❌ Produto não encontrado.',
                    ephemeral: true
                });
            }

            // Verifica se a loja pertence ao servidor atual
            if (product.storeId.guildId !== interaction.guild.id) {
                return await interaction.reply({
                    content: '❌ Este produto não pertence a este servidor.',
                    ephemeral: true
                });
            }

            // Conta itens de estoque
            const stockCount = await StockItem.countDocuments({
                productId,
                status: 'available'
            });

            const embed = new EmbedBuilder()
                .setColor('#0099ff')
                .setTitle(`📦 ${product.name}`)
                .setDescription(product.description || 'Sem descrição')
                .addFields(
                    { name: '💰 Preço', value: `R$ ${product.price.toFixed(2)}`, inline: true },
                    { name: '📂 Categoria', value: product.category, inline: true },
                    { name: '📊 Status', value: product.status, inline: true },
                    { name: '📦 Estoque', value: `${stockCount} itens disponíveis`, inline: true },
                    { name: '🏪 Loja', value: product.storeId.name, inline: true },
                    { name: '🆔 ID', value: `\`${product._id}\``, inline: true },
                    { name: '📅 Criado em', value: product.createdAt.toLocaleDateString('pt-BR'), inline: true },
                    { name: '👤 Criado por', value: `<@${product.createdBy}>`, inline: true }
                )
                .setTimestamp()
                .setFooter({ text: `Visualizado por ${interaction.user.tag}` });

            if (product.lastModifiedBy) {
                embed.addFields({
                    name: '✏️ Última modificação',
                    value: `<@${product.lastModifiedBy}> em ${product.updatedAt.toLocaleDateString('pt-BR')}`,
                    inline: false
                });
            }

            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro ao visualizar produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao visualizar o produto.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula botões de busca
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} customId - ID customizado completo
     */
    static async handleSearchButton(interaction, action, customId) {
        try {
            logger.info(`Botão de busca clicado: ${customId} por ${interaction.user.tag}`);

            // Parse do customId para botões de busca: search_stock_productId
            if (customId.startsWith('search_stock_')) {
                await this.handleStockSearchButton(interaction, customId);
                return;
            }

            await interaction.reply({
                content: '❌ Ação de busca não reconhecida.',
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de botão de busca:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula botões de debug
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do botão
     * @param {string} id - ID relacionado ao botão
     */
    static async handleDebugButton(interaction, action, id) {
        try {
            // Verificação de permissões
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem usar funções de debug.',
                    ephemeral: true
                });
            }

            logger.info(`Botão de debug clicado: ${action} (ID: ${id}) por ${interaction.user.tag}`);

            await interaction.reply({
                content: `🐛 Funcionalidade de debug será implementada em breve! (ID: ${id})`,
                ephemeral: true
            });
        } catch (error) {
            logger.error('Erro no handler de botão de debug:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Confirma exclusão de estoque
     * @param {Object} interaction - Interação do Discord
     * @param {string} customId - ID customizado completo
     */
    static async handleConfirmDeleteStock(interaction, customId) {
        await interaction.reply({
            content: '✅ Exclusão de estoque confirmada! (Funcionalidade será implementada)',
            ephemeral: true
        });
    }

    /**
     * Confirma exclusão de produto
     * @param {Object} interaction - Interação do Discord
     * @param {string} customId - ID customizado completo
     */
    static async handleConfirmDeleteProduct(interaction, customId) {
        try {
            // Parse do customId: confirm_delete_product_productId
            const productId = customId.replace('confirm_delete_product_', '');

            // Importa o AdminModalHandler para reutilizar a lógica
            const { AdminModalHandler } = await import('../modals/adminModalHandler.js');
            await AdminModalHandler.handleConfirmDeleteProduct(interaction, productId);

        } catch (error) {
            logger.error('Erro no handler de confirmação de exclusão de produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar a exclusão do produto.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula busca de estoque
     * @param {Object} interaction - Interação do Discord
     * @param {string} customId - ID customizado completo
     */
    static async handleStockSearchButton(interaction, customId) {
        await interaction.reply({
            content: '🔍 Busca de estoque será implementada em breve!',
            ephemeral: true
        });
    }
}
