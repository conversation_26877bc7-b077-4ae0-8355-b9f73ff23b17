import { logger } from '../../utils/logging/logger.js';
import ShoppingCartHandler from '../cart/shoppingCartHandler.js';
import CartButtonHandler from '../cart/cartButtonHandler.js';
import { StoreButtonHandler } from '../buttons/storeButtonHandler.js';
import { AdminButtonHandler } from '../buttons/adminButtonHandler.js';
import { ConfigButtonHandler } from '../buttons/configButtonHandler.js';
import { ActionButtonHandler } from '../buttons/actionButtonHandler.js';


/**
 * Manipula interações de botões
 * @param {ButtonInteraction} interaction 
 */
export async function handleButton(interaction) {
    const customId = interaction.customId;
    
    try {
        logger.info(`Botão clicado: ${customId} por ${interaction.user.tag}`);

        // Roteamento baseado no customId do botão
        // Para config: config_admin_logs, config_public_logs, etc.
        // Para outros: categoria_acao_id
        const parts = customId.split('_');
        const category = parts[0];
        
        let action, id;
        
        if (category === 'config') {
            // Para botões de config, junta todas as partes após 'config_' como action
            action = parts.slice(1).join('_');
            id = null;
        } else {
            // Para outros botões, mantém o formato original
            action = parts[1];
            id = parts[2];
        }

        switch (category) {
            case 'store':
                await StoreButtonHandler.handleStoreButton(interaction, action, id);
                break;
            case 'admin':
                await AdminButtonHandler.handleAdminButton(interaction, action, id);
                break;
            case 'debug':
                await ActionButtonHandler.handleDebugButton(interaction, action, id);
                break;
            case 'delete':
                await ActionButtonHandler.handleDeleteButton(interaction, action, id);
                break;
            case 'config':
                await ConfigButtonHandler.handleConfigButton(interaction, action, id);
                break;
            case 'confirm':
                await ActionButtonHandler.handleConfirmButton(interaction, action, customId);
                break;
            case 'cancel':
                await ActionButtonHandler.handleCancelButton(interaction, action, customId);
                break;
            case 'view':
                await ActionButtonHandler.handleViewButton(interaction, action, customId);
                break;
            case 'search':
                await ActionButtonHandler.handleSearchButton(interaction, action, customId);
                break;
            case 'cart':
                // Verifica se é um dos novos botões do carrinho fixo
                if (['cart_clear', 'cart_checkout', 'cart_cancel_payment', 'cart_check_payment_status', 'cart_manage_items', 'cart_confirm_cancel', 'cart_dismiss_cancel', 'cart_cancel_purchase', 'cart_copy_pix'].includes(customId)) {
                    await CartButtonHandler.handleCartButton(interaction);
                } else {
                    // Botões antigos do carrinho
                    await ShoppingCartHandler.handleCartButton(interaction);
                }
                break;
            case 'edit':
                // Botões de edição de produto: edit_product_basic_productId, edit_product_price_productId, etc.
                if (customId.startsWith('edit_product_')) {
                    await AdminButtonHandler.handleEditProductButton(interaction, customId);
                } else {
                    await AdminButtonHandler.handleEditButton(interaction, id);
                }
                break;
            default:
                // Verifica se é um botão específico do carrinho
                if (customId === 'cart_confirm_purchase') {
                    await ShoppingCartHandler.handleConfirmPurchase(interaction);
                } else {
                    logger.warn(`Categoria de botão não reconhecida: ${category}`);
                    await interaction.reply({
                        content: '❌ Botão não reconhecido.',
                        ephemeral: true
                    });
                }
        }

    } catch (error) {
        logger.error(`Erro ao processar botão ${customId}:`, error);
        
        try {
            const errorMessage = {
                content: '❌ Erro ao processar a ação do botão.',
                ephemeral: true
            };

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply(errorMessage);
            } else if (interaction.deferred) {
                await interaction.editReply({
                    content: '❌ Erro ao processar a ação do botão.'
                });
            } else if (interaction.replied) {
                await interaction.followUp(errorMessage);
            }
        } catch (replyError) {
            logger.error(`Erro ao responder após falha no botão ${customId}:`, replyError);
        }
    }
}
































