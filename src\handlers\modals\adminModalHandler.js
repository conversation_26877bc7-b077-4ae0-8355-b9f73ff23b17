import { PermissionFlagsBits } from 'discord.js';
import { logger } from '../../utils/logging/logger.js';
import Product from '../../models/Product.js';
import Store from '../../models/Store.js';
import { VALIDATION } from '../../config/constants.js';

/**
 * Handler para modais administrativos
 */
export class AdminModalHandler {
    /**
     * Manipula modais administrativos
     * @param {Object} interaction - Interação do Discord
     * @param {string} action - Ação do modal
     */
    static async handleAdminModal(interaction, action) {
        try {
            // Verificação básica de permissões
            if (!interaction.member.permissions.has(PermissionFlagsBits.Administrator)) {
                return await interaction.reply({
                    content: '❌ Você não tem permissão para usar este modal.',
                    ephemeral: true
                });
            }

            logger.info(`Modal administrativo enviado: ${action} por ${interaction.user.tag}`);

            switch (action) {
                case 'addproduct':
                    await this.handleAddProductModal(interaction);
                    break;
                case 'editproduct':
                    await this.handleEditProductModal(interaction);
                    break;
                case 'deleteproduct':
                    await this.handleDeleteProductModal(interaction);
                    break;
                case 'managestore':
                    await this.handleManageStoreModal(interaction);
                    break;
                default:
                    await interaction.reply({
                        content: '❌ Ação de modal administrativo não reconhecida.',
                        ephemeral: true
                    });
            }
        } catch (error) {
            logger.error('Erro no handler de modal administrativo:', error);
            
            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar sua solicitação.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de adição de produto
     * @param {Object} interaction - Interação do Discord
     */
    static async handleAddProductModal(interaction) {
        const productName = interaction.fields.getTextInputValue('product_name');
        const productPrice = interaction.fields.getTextInputValue('product_price');
        
        await interaction.reply({
            content: `✅ Produto "${productName}" adicionado com preço R$ ${productPrice}`,
            ephemeral: true
        });
    }

    /**
     * Manipula modal de edição de produto
     * @param {Object} interaction - Interação do Discord
     */
    static async handleEditProductModal(interaction) {
        const customId = interaction.customId;

        try {
            // Parse do customId para determinar o tipo de edição
            if (customId.startsWith('edit_product_basic_modal_')) {
                const productId = customId.replace('edit_product_basic_modal_', '');
                await this.handleEditProductBasicModal(interaction, productId);
            } else if (customId.startsWith('edit_product_price_modal_')) {
                const productId = customId.replace('edit_product_price_modal_', '');
                await this.handleEditProductPriceModal(interaction, productId);
            } else {
                await interaction.reply({
                    content: '✏️ Funcionalidade de edição de produto será implementada em breve!',
                    ephemeral: true
                });
            }
        } catch (error) {
            logger.error('Erro no handler de modal de edição de produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar a edição do produto.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de edição básica de produto
     * @param {Object} interaction - Interação do Discord
     * @param {string} productId - ID do produto
     */
    static async handleEditProductBasicModal(interaction, productId) {
        try {
            logger.info(`Modal de edição básica de produto enviado: ${productId} por ${interaction.user.tag}`);

            // Extrai os dados do modal
            const productName = interaction.fields.getTextInputValue('product_name').trim();
            const productDescription = interaction.fields.getTextInputValue('product_description')?.trim() || '';
            const productEmoji = interaction.fields.getTextInputValue('product_emoji')?.trim() || null;

            // Validações básicas
            if (!productName || productName.length < VALIDATION.PRODUCT_NAME.MIN_LENGTH) {
                return await interaction.reply({
                    content: `❌ O nome do produto deve ter pelo menos ${VALIDATION.PRODUCT_NAME.MIN_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            if (productName.length > VALIDATION.PRODUCT_NAME.MAX_LENGTH) {
                return await interaction.reply({
                    content: `❌ O nome do produto deve ter no máximo ${VALIDATION.PRODUCT_NAME.MAX_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            if (productDescription.length > VALIDATION.PRODUCT_DESCRIPTION.MAX_LENGTH) {
                return await interaction.reply({
                    content: `❌ A descrição deve ter no máximo ${VALIDATION.PRODUCT_DESCRIPTION.MAX_LENGTH} caracteres.`,
                    ephemeral: true
                });
            }

            // Busca o produto
            const product = await Product.findById(productId).populate('storeId');
            if (!product) {
                return await interaction.reply({
                    content: '❌ Produto não encontrado.',
                    ephemeral: true
                });
            }

            // Verifica se a loja pertence ao servidor atual
            if (product.storeId.guildId !== interaction.guild.id) {
                return await interaction.reply({
                    content: '❌ Este produto não pertence a este servidor.',
                    ephemeral: true
                });
            }

            // Verifica se já existe outro produto com o mesmo nome na loja (exceto o atual)
            const existingProduct = await Product.findOne({
                storeId: product.storeId._id,
                name: { $regex: new RegExp(`^${productName}$`, 'i') },
                status: { $ne: 'discontinued' },
                _id: { $ne: productId }
            });

            if (existingProduct) {
                return await interaction.reply({
                    content: `❌ Já existe outro produto com o nome "${productName}" nesta loja.`,
                    ephemeral: true
                });
            }

            // Atualiza o produto
            const oldName = product.name;
            product.name = productName;
            product.description = productDescription || product.description;
            product.emoji = productEmoji;
            product.lastModifiedBy = interaction.user.id;

            await product.save();

            logger.info(`Produto "${oldName}" editado para "${productName}" por ${interaction.user.tag}`);

            await interaction.reply({
                content: `✅ **Produto atualizado com sucesso!**\n\n` +
                        `📦 **Nome:** ${productName}\n` +
                        `📝 **Descrição:** ${productDescription || 'Não informada'}\n` +
                        `🏪 **Loja:** ${product.storeId.name}\n` +
                        `${productEmoji ? `${productEmoji} **Emoji:** ${productEmoji}\n` : ''}`,
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro no handler de modal de edição básica de produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro interno ao editar o produto. Tente novamente mais tarde.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de edição de preço de produto
     * @param {Object} interaction - Interação do Discord
     * @param {string} productId - ID do produto
     */
    static async handleEditProductPriceModal(interaction, productId) {
        try {
            logger.info(`Modal de edição de preço de produto enviado: ${productId} por ${interaction.user.tag}`);

            // Extrai os dados do modal
            const productPriceStr = interaction.fields.getTextInputValue('product_price').trim();
            const originalPriceStr = interaction.fields.getTextInputValue('product_original_price')?.trim() || '';

            // Validação e conversão do preço
            const productPrice = parseFloat(productPriceStr.replace(',', '.'));
            if (isNaN(productPrice) || productPrice < VALIDATION.PRODUCT_PRICE.MIN) {
                return await interaction.reply({
                    content: `❌ Preço inválido. O valor deve ser um número maior que R$ ${VALIDATION.PRODUCT_PRICE.MIN.toFixed(2)}.`,
                    ephemeral: true
                });
            }

            if (productPrice > VALIDATION.PRODUCT_PRICE.MAX) {
                return await interaction.reply({
                    content: `❌ Preço muito alto. O valor máximo é R$ ${VALIDATION.PRODUCT_PRICE.MAX.toFixed(2)}.`,
                    ephemeral: true
                });
            }

            // Validação do preço original (se fornecido)
            let originalPrice = null;
            if (originalPriceStr) {
                originalPrice = parseFloat(originalPriceStr.replace(',', '.'));
                if (isNaN(originalPrice) || originalPrice < VALIDATION.PRODUCT_PRICE.MIN) {
                    return await interaction.reply({
                        content: `❌ Preço original inválido. O valor deve ser um número maior que R$ ${VALIDATION.PRODUCT_PRICE.MIN.toFixed(2)}.`,
                        ephemeral: true
                    });
                }

                if (originalPrice <= productPrice) {
                    return await interaction.reply({
                        content: `❌ O preço original deve ser maior que o preço atual para configurar uma promoção.`,
                        ephemeral: true
                    });
                }
            }

            // Busca o produto
            const product = await Product.findById(productId).populate('storeId');
            if (!product) {
                return await interaction.reply({
                    content: '❌ Produto não encontrado.',
                    ephemeral: true
                });
            }

            // Verifica se a loja pertence ao servidor atual
            if (product.storeId.guildId !== interaction.guild.id) {
                return await interaction.reply({
                    content: '❌ Este produto não pertence a este servidor.',
                    ephemeral: true
                });
            }

            // Atualiza o produto
            const oldPrice = product.price;
            const oldOriginalPrice = product.originalPrice;

            product.price = productPrice;
            product.originalPrice = originalPrice;
            product.lastModifiedBy = interaction.user.id;

            await product.save();

            logger.info(`Preço do produto "${product.name}" alterado de R$ ${oldPrice} para R$ ${productPrice} por ${interaction.user.tag}`);

            const isOnSale = originalPrice && originalPrice > productPrice;
            const discountPercentage = isOnSale ? Math.round(((originalPrice - productPrice) / originalPrice) * 100) : 0;

            await interaction.reply({
                content: `✅ **Preço atualizado com sucesso!**\n\n` +
                        `📦 **Produto:** ${product.name}\n` +
                        `💰 **Preço anterior:** R$ ${oldPrice.toFixed(2)}\n` +
                        `💰 **Novo preço:** R$ ${productPrice.toFixed(2)}\n` +
                        `${isOnSale ? `🏷️ **Preço original:** R$ ${originalPrice.toFixed(2)} (${discountPercentage}% de desconto)\n` : ''}` +
                        `🏪 **Loja:** ${product.storeId.name}`,
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro no handler de modal de edição de preço de produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro interno ao alterar o preço do produto. Tente novamente mais tarde.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de exclusão de produto
     * @param {Object} interaction - Interação do Discord
     */
    static async handleDeleteProductModal(interaction) {
        try {
            const customId = interaction.customId;

            // Parse do customId para obter o ID do produto
            if (customId.startsWith('confirm_delete_product_')) {
                const productId = customId.replace('confirm_delete_product_', '');
                await this.handleConfirmDeleteProduct(interaction, productId);
            } else {
                await interaction.reply({
                    content: '🗑️ Funcionalidade de exclusão de produto será implementada em breve!',
                    ephemeral: true
                });
            }
        } catch (error) {
            logger.error('Erro no handler de modal de exclusão de produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro ao processar a exclusão do produto.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Confirma e executa a exclusão de produto
     * @param {Object} interaction - Interação do Discord
     * @param {string} productId - ID do produto
     */
    static async handleConfirmDeleteProduct(interaction, productId) {
        try {
            logger.info(`Confirmação de exclusão de produto: ${productId} por ${interaction.user.tag}`);

            // Busca o produto
            const product = await Product.findById(productId).populate('storeId');
            if (!product) {
                return await interaction.reply({
                    content: '❌ Produto não encontrado.',
                    ephemeral: true
                });
            }

            // Verifica se a loja pertence ao servidor atual
            if (product.storeId.guildId !== interaction.guild.id) {
                return await interaction.reply({
                    content: '❌ Este produto não pertence a este servidor.',
                    ephemeral: true
                });
            }

            // Conta itens de estoque associados
            const stockCount = await StockItem.countDocuments({
                productId,
                status: 'available'
            });

            // Remove todos os itens de estoque associados
            if (stockCount > 0) {
                await StockItem.deleteMany({ productId });
                logger.info(`${stockCount} itens de estoque removidos para o produto ${product.name}`);
            }

            // Remove o produto (soft delete - marca como discontinued)
            product.status = 'discontinued';
            product.lastModifiedBy = interaction.user.id;
            product.deletedAt = new Date();
            await product.save();

            logger.info(`Produto "${product.name}" marcado como descontinuado por ${interaction.user.tag}`);

            await interaction.reply({
                content: `✅ **Produto removido com sucesso!**\n\n` +
                        `📦 **Produto:** ${product.name}\n` +
                        `🏪 **Loja:** ${product.storeId.name}\n` +
                        `🗑️ **Itens de estoque removidos:** ${stockCount}\n` +
                        `📅 **Removido em:** ${new Date().toLocaleDateString('pt-BR')}\n\n` +
                        `ℹ️ O produto foi marcado como descontinuado e não aparecerá mais nas listagens.`,
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro ao confirmar exclusão de produto:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro interno ao remover o produto. Tente novamente mais tarde.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de gerenciamento de loja
     * @param {Object} interaction - Interação do Discord
     */
    static async handleManageStoreModal(interaction) {
        await interaction.reply({
            content: '⚙️ Funcionalidade de gerenciamento de loja será implementada em breve!',
            ephemeral: true
        });
    }

    /**
     * Manipula modal de configuração de usuário
     * @param {Object} interaction - Interação do Discord
     */
    static async handleUserConfigModal(interaction) {
        await interaction.reply({
            content: '👤 Funcionalidade de configuração de usuário será implementada em breve!',
            ephemeral: true
        });
    }

    /**
     * Manipula modal de configuração de servidor
     * @param {Object} interaction - Interação do Discord
     */
    static async handleServerConfigModal(interaction) {
        await interaction.reply({
            content: '🏠 Funcionalidade de configuração de servidor será implementada em breve!',
            ephemeral: true
        });
    }

    /**
     * Manipula modal de relatórios
     * @param {Object} interaction - Interação do Discord
     */
    static async handleReportsModal(interaction) {
        try {
            // Verificação de permissões
            if (!interaction.member.permissions.has('Administrator')) {
                return await interaction.reply({
                    content: '❌ Apenas administradores podem gerar relatórios.',
                    ephemeral: true
                });
            }

            logger.info(`Relatório solicitado por ${interaction.user.tag}`);

            // Busca dados do servidor
            const stores = await Store.find({
                guildId: interaction.guild.id,
                isActive: true
            });

            if (stores.length === 0) {
                return await interaction.reply({
                    content: '❌ Nenhuma loja encontrada neste servidor.',
                    ephemeral: true
                });
            }

            const storeIds = stores.map(store => store._id);

            // Estatísticas de produtos
            const totalProducts = await Product.countDocuments({
                storeId: { $in: storeIds }
            });

            const activeProducts = await Product.countDocuments({
                storeId: { $in: storeIds },
                status: 'active'
            });

            const outOfStockProducts = await Product.countDocuments({
                storeId: { $in: storeIds },
                status: 'out_of_stock'
            });

            // Estatísticas de estoque
            const totalStockItems = await StockItem.countDocuments({
                productId: { $in: await Product.find({ storeId: { $in: storeIds } }).distinct('_id') }
            });

            const availableStockItems = await StockItem.countDocuments({
                productId: { $in: await Product.find({ storeId: { $in: storeIds } }).distinct('_id') },
                status: 'available'
            });

            // Cria embed do relatório
            const embed = new EmbedBuilder()
                .setColor('#00ff00')
                .setTitle('📊 Relatório do Servidor')
                .setDescription(`Estatísticas gerais das lojas em **${interaction.guild.name}**`)
                .addFields(
                    {
                        name: '🏪 Lojas',
                        value: `**${stores.length}** lojas ativas`,
                        inline: true
                    },
                    {
                        name: '📦 Produtos',
                        value: `**${totalProducts}** produtos totais\n` +
                               `🟢 **${activeProducts}** ativos\n` +
                               `🔴 **${outOfStockProducts}** sem estoque`,
                        inline: true
                    },
                    {
                        name: '📋 Estoque',
                        value: `**${totalStockItems}** itens totais\n` +
                               `✅ **${availableStockItems}** disponíveis`,
                        inline: true
                    }
                )
                .setTimestamp()
                .setFooter({
                    text: `Relatório gerado por ${interaction.user.tag}`,
                    iconURL: interaction.user.displayAvatarURL()
                });

            // Adiciona detalhes das lojas
            if (stores.length <= 10) {
                const storeDetails = stores.map(store => {
                    const channel = interaction.guild.channels.cache.get(store.channelId);
                    return `• **${store.name}** - ${channel ? channel.toString() : 'Canal não encontrado'}`;
                }).join('\n');

                embed.addFields({
                    name: '🏪 Detalhes das Lojas',
                    value: storeDetails,
                    inline: false
                });
            }

            await interaction.reply({
                embeds: [embed],
                ephemeral: true
            });

        } catch (error) {
            logger.error('Erro no handler de modal de relatórios:', error);

            if (!interaction.replied && !interaction.deferred) {
                await interaction.reply({
                    content: '❌ Ocorreu um erro interno ao gerar o relatório. Tente novamente mais tarde.',
                    ephemeral: true
                });
            }
        }
    }

    /**
     * Manipula modal de backup
     * @param {Object} interaction - Interação do Discord
     */
    static async handleBackupModal(interaction) {
        await interaction.reply({
            content: '💾 Funcionalidade de backup será implementada em breve!',
            ephemeral: true
        });
    }

    /**
     * Manipula modal de importação
     * @param {Object} interaction - Interação do Discord
     */
    static async handleImportModal(interaction) {
        await interaction.reply({
            content: '📥 Funcionalidade de importação será implementada em breve!',
            ephemeral: true
        });
    }

    /**
     * Manipula modal de exportação
     * @param {Object} interaction - Interação do Discord
     */
    static async handleExportModal(interaction) {
        await interaction.reply({
            content: '📤 Funcionalidade de exportação será implementada em breve!',
            ephemeral: true
        });
    }
}
